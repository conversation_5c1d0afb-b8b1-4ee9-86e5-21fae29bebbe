package com.hys.hm.application.referral.converter;

import com.hys.hm.application.referral.dto.ReferralCreateDTO;
import com.hys.hm.application.referral.dto.ReferralQueryDTO;
import com.hys.hm.domain.referral.model.HospitalInfo;
import com.hys.hm.domain.referral.model.MedicalInfo;
import com.hys.hm.domain.referral.model.PatientInfo;
import com.hys.hm.domain.referral.model.ReferralForm;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 转诊DTO转换器
 * 负责应用层DTO与领域模型之间的转换
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Component
public class ReferralDTOConverter {
    
    /**
     * 创建DTO转换为领域模型
     */
    public ReferralForm toReferralForm(ReferralCreateDTO createDTO) {
        if (createDTO == null) {
            return null;
        }
        
        // 构建患者信息
        PatientInfo patientInfo = PatientInfo.builder()
                .patientId(createDTO.getBasicInfoId())
                .name(createDTO.getPatientName())
                .gender(createDTO.getGender())
                .age(createDTO.getAge())
                .idCard(createDTO.getIdCard())
                .phone(createDTO.getPhone())
                .address(createDTO.getAddress())
                .addressDetail(createDTO.getAddressDetail())
                .fileNumber(createDTO.getFileNumber())
                .build();
        
        // 构建医疗信息
        MedicalInfo medicalInfo = MedicalInfo.builder()
                .mainSymptoms(createDTO.getMainSymptoms())
                .preliminaryDiagnosis(createDTO.getPreliminaryDiagnosis())
                .medicalHistory(createDTO.getMedicalHistory())
                .examResults(createDTO.getExamResults())
                .treatmentHistory(createDTO.getTreatmentHistory())
                .referralReason(createDTO.getReferralReason())
                .referralPurpose(createDTO.getReferralPurpose())
                .build();
        
        // 构建转出医院信息
        HospitalInfo outHospital = HospitalInfo.builder()
                .unitId(createDTO.getOutUnitId())
                .unitName(createDTO.getOutUnitName())
                .deptId(createDTO.getOutDeptId())
                .deptName(createDTO.getOutDeptName())
                .doctorId(createDTO.getOutDoctorId())
                .doctorName(createDTO.getOutDoctorName())
                .doctorPhone(createDTO.getOutDoctorPhone())
                .build();
        
        // 构建转入医院信息
        HospitalInfo inHospital = HospitalInfo.builder()
                .unitId(createDTO.getInUnitId())
                .unitName(createDTO.getInUnitName())
                .deptId(createDTO.getInDeptId())
                .deptName(createDTO.getInDeptName())
                .doctorId(createDTO.getInDoctorId())
                .doctorName(createDTO.getInDoctorName())
                .doctorPhone(createDTO.getInDoctorPhone())
                .build();
        
        // 构建转诊表单
        return ReferralForm.builder()
                .basicInfoId(createDTO.getBasicInfoId())
                .referralDate(createDTO.getReferralDate() != null ? createDTO.getReferralDate() : LocalDateTime.now())
                .patientInfo(patientInfo)
                .medicalInfo(medicalInfo)
                .outHospital(outHospital)
                .inHospital(inHospital)
                .urgencyLevel(createDTO.getUrgencyLevel())
                .appointmentTime(createDTO.getAppointmentTime())
                .attachments(createDTO.getAttachments())
                .notes(createDTO.getNotes())
                .build();
    }
    
    /**
     * 领域模型转换为查询DTO
     */
    public ReferralQueryDTO toQueryDTO(ReferralForm referralForm) {
        if (referralForm == null) {
            return null;
        }
        
        ReferralQueryDTO.ReferralQueryDTOBuilder builder = ReferralQueryDTO.builder()
                .id(referralForm.getId())
                .basicInfoId(referralForm.getBasicInfoId())
                .referralNo(referralForm.getReferralNo())
                .referralDate(referralForm.getReferralDate())
                .status(referralForm.getStatus())
                .rejectReason(referralForm.getRejectReason())
                .confirmTime(referralForm.getConfirmTime())
                .urgencyLevel(referralForm.getUrgencyLevel())
                .appointmentTime(referralForm.getAppointmentTime())
                .attachments(referralForm.getAttachments())
                .notes(referralForm.getNotes())
                .createTime(referralForm.getCreateTime())
                .updateTime(referralForm.getUpdateTime())
                .createBy(referralForm.getCreateBy())
                .updateBy(referralForm.getUpdateBy())
                .version(referralForm.getVersion());
        
        // 患者信息
        if (referralForm.getPatientInfo() != null) {
            PatientInfo patientInfo = referralForm.getPatientInfo();
            builder.patientName(patientInfo.getName())
                    .gender(patientInfo.getGender())
                    .age(patientInfo.getAge())
                    .idCard(maskIdCard(patientInfo.getIdCard()))
                    .phone(maskPhone(patientInfo.getPhone()))
                    .address(patientInfo.getAddress())
                    .addressDetail(patientInfo.getAddressDetail())
                    .fileNumber(patientInfo.getFileNumber());
        }
        
        // 医疗信息
        if (referralForm.getMedicalInfo() != null) {
            MedicalInfo medicalInfo = referralForm.getMedicalInfo();
            builder.mainSymptoms(medicalInfo.getMainSymptoms())
                    .preliminaryDiagnosis(medicalInfo.getPreliminaryDiagnosis())
                    .medicalHistory(medicalInfo.getMedicalHistory())
                    .examResults(medicalInfo.getExamResults())
                    .treatmentHistory(medicalInfo.getTreatmentHistory())
                    .referralReason(medicalInfo.getReferralReason())
                    .referralPurpose(medicalInfo.getReferralPurpose());
        }
        
        // 转出医院信息
        if (referralForm.getOutHospital() != null) {
            HospitalInfo outHospital = referralForm.getOutHospital();
            builder.outUnitId(outHospital.getUnitId())
                    .outUnitName(outHospital.getUnitName())
                    .outDeptId(outHospital.getDeptId())
                    .outDeptName(outHospital.getDeptName())
                    .outDoctorId(outHospital.getDoctorId())
                    .outDoctorName(outHospital.getDoctorName())
                    .outDoctorPhone(outHospital.getDoctorPhone());
        }
        
        // 转入医院信息
        if (referralForm.getInHospital() != null) {
            HospitalInfo inHospital = referralForm.getInHospital();
            builder.inUnitId(inHospital.getUnitId())
                    .inUnitName(inHospital.getUnitName())
                    .inDeptId(inHospital.getDeptId())
                    .inDeptName(inHospital.getDeptName())
                    .inDoctorId(inHospital.getDoctorId())
                    .inDoctorName(inHospital.getDoctorName())
                    .inDoctorPhone(inHospital.getDoctorPhone());
        }
        
        return builder.build();
    }
    
    /**
     * 脱敏身份证号
     */
    private String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }
    
    /**
     * 脱敏手机号
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
}
